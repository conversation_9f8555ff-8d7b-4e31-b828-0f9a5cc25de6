<project
	xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.curefit</groupId>
		<artifactId>ticketing-system</artifactId>
		<version>2.1.9</version>
	</parent>
	<artifactId>ticket-management-core</artifactId>
	<properties>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.flywaydb</groupId>
			<artifactId>flyway-core</artifactId>
			<version>9.22.0</version> <!-- Use the latest compatible version -->
		</dependency>
		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-ui</artifactId>
			<version>1.7.0</version>
		</dependency>
		<dependency>
			<groupId>org.jsoup</groupId>
			<artifactId>jsoup</artifactId>
			<version>1.18.3</version>
		</dependency>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>user-service-client</artifactId>
			<version>3.0.2</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>2.17.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>2.17.0</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.dataformat</groupId>
			<artifactId>jackson-dataformat-csv</artifactId>
			<version>2.7.0</version>
		</dependency>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>cf-commons</artifactId>
			<version>${curefit.common.version}</version>
		</dependency>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>cf-commons-domain</artifactId>
			<version>${curefit.common.version}</version>
		</dependency>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>ticket-management-commons</artifactId>
			<version>${project.parent.version}</version>
		</dependency>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>ticket-management-client</artifactId>
			<version>${project.parent.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>
		<dependency>
			<groupId>net.javacrumbs.shedlock</groupId>
			<artifactId>shedlock-spring</artifactId>
			<version>2.5.0</version>
		</dependency>

		<dependency>
			<groupId>net.javacrumbs.shedlock</groupId>
			<artifactId>shedlock-provider-redis-spring</artifactId>
			<version>2.5.0</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.curefit.commons</groupId>
			<artifactId>common-test</artifactId>
			<version>${cf.common.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.jayway.jsonpath</groupId>
			<artifactId>json-path</artifactId>
			<version>2.9.0</version><!--$NO-MVN-MAN-VER$ -->
			<exclusions>
				<exclusion>
					<groupId>net.minidev</groupId>
					<artifactId>json-smart</artifactId>
				</exclusion>
				<exclusion>
					<groupId>net.minidev</groupId>
					<artifactId>accessors-smart</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>net.minidev</groupId>
			<artifactId>json-smart</artifactId>
			<version>2.4.11</version>
		</dependency>

		<dependency>
			<groupId>net.minidev</groupId>
			<artifactId>accessors-smart</artifactId>
			<version>2.4.11</version>
		</dependency>

		<dependency>
			<groupId>com.curefit.commons</groupId>
			<artifactId>common-rest-client</artifactId>
			<version>${cf.common.version}</version>
		</dependency>
		<dependency>
			<groupId>com.curefit.iris</groupId>
			<artifactId>iris-client</artifactId>
			<version>2.0.8</version>
		</dependency>
		<!-- Json String Type -->
		<dependency>
			<groupId>com.vladmihalcea</groupId>
			<artifactId>hibernate-types-52</artifactId>
			<version>2.3.4</version>
		</dependency>

		<dependency>
			<groupId>com.google.api-client</groupId>
			<artifactId>google-api-client</artifactId>
			<version>1.23.0</version>
		</dependency>

		<dependency>
			<groupId>com.curefit.report-issues</groupId>
			<artifactId>report-issues-client</artifactId>
			<version>2.0.0</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>rashi-client</artifactId>
			<version>3.8.3</version>
			<exclusions>
				<exclusion>
					<groupId>com.google.collections</groupId>
					<artifactId>google-collections</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.curefit.report-issues</groupId>
			<artifactId>report-issues-commons</artifactId>
			<version>2.0.0</version>
		</dependency>

		<dependency>
			<groupId>com.google.oauth-client</groupId>
			<artifactId>google-oauth-client-jetty</artifactId>
			<version>1.23.0</version>
		</dependency>

		<dependency>
			<groupId>com.google.apis</groupId>
			<artifactId>google-api-services-admin-directory</artifactId>
			<version>directory_v1-rev110-1.25.0</version>
		</dependency>

		<dependency>
			<groupId>com.google.apis</groupId>
			<artifactId>google-api-services-gmail</artifactId>
			<version>v1-rev105-1.25.0</version>
		</dependency>

		<dependency>
			<artifactId>freshdesk-client</artifactId>
			<groupId>com.curefit</groupId>
			<version>1.0.6</version>
		</dependency>

		<dependency>
			<artifactId>mozart-client</artifactId>
			<groupId>com.curefit</groupId>
			<version>1.2.0</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>gymfit-client</artifactId>
			<version>${gymfit.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.hibernate.javax.persistence</groupId>
					<artifactId>hibernate-jpa-2.0-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.amazonaws</groupId>
					<artifactId>aws-java-sdk-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.amazonaws</groupId>
					<artifactId>aws-java-sdk-sns</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.amazonaws</groupId>
					<artifactId>jmespath-java</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-cache</artifactId>
		</dependency>
		<dependency>
			<groupId>dev.shortloop.agent</groupId>
			<artifactId>agent-java</artifactId>
			<version>0.0.12</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.retry</groupId>
			<artifactId>spring-retry</artifactId>
			<version>2.0.12</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aspects</artifactId>
			<version>5.2.8.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>config-store-client</artifactId>
			<version>1.2.0</version>
			<scope>compile</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.11.0</version>
				<configuration>
					<annotationProcessorPaths>
						<path>
							<groupId>org.mapstruct</groupId>
							<artifactId>mapstruct-processor</artifactId>
							<version>1.5.3.Final</version>
						</path>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
							<version>${lombok.version}</version>
						</path>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok-mapstruct-binding</artifactId>
							<version>0.2.0</version>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>${spring-boot.version}</version>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>3.0.0</version>
				<configuration>
					<useSystemClassLoader>false</useSystemClassLoader>
					<argLine>--add-opens java.base/java.lang=ALL-UNNAMED</argLine>
					<includes>
						<include>**/*Test.java</include>
						<include>**/*Tests.java</include>
					</includes>
				</configuration>
				<dependencies>
					<dependency>
						<groupId>org.apache.maven.surefire</groupId>
						<artifactId>surefire-junit4</artifactId>
						<version>3.0.0</version>
					</dependency>
				</dependencies>
			</plugin>

		</plugins>
	</build>

</project>
