package com.curefit.ticket.management.service.test;

import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.admin.service.CategoryService;
import com.curefit.odin.admin.service.CustomFieldService;
import com.curefit.odin.admin.service.SubCategoryService;
import com.curefit.odin.admin.service.UserService;
import com.curefit.odin.enums.Priority;
import com.curefit.odin.enums.Status;
import com.curefit.odin.user.models.FieldData;
import com.curefit.odin.user.models.Ticket;
import com.curefit.odin.user.pojo.AttachmentEntry;
import com.curefit.odin.user.pojo.FieldDataEntry;
import com.curefit.odin.user.pojo.TicketEntry;
import com.curefit.odin.user.repositories.TicketDAO;
import com.curefit.odin.user.service.AttachmentService;
import com.curefit.odin.user.service.CommentService;
import com.curefit.odin.user.service.FieldDataService;
import com.curefit.odin.user.service.TicketService;
import com.curefit.odin.user.service.TicketWatcherService;
import com.curefit.ticket.management.test.helper.PojoGenerator;

@RunWith(MockitoJUnitRunner.class)
public class TicketTest {

  private static final long TICKET_ID = 1L;
  private static final long FIELD_ID = 4L;
  private static final long FIELD_DATA_ID = 4L;
  private static final String WATCHER_ID = "<EMAIL>";
  private static final long MESSAGE_ID = 7L;
  private static final long ATTACHMENT_ID = 8L;

  private static final long SUB_CATEGORY_ID = 3L;

  private static final long CATEGORY_ID = 2L;
  private static final long ROLE_ID = 4L;
  private static final long ENTITY_ID = 4L;

  @Mock
  private TicketDAO ticketDao;
  @Mock
  private FieldDataService fieldDataService;
  @Mock
  private CategoryService ticketCategoryService;
  @Mock
  private SubCategoryService ticketSubCategoryService;
  @Mock
  private TicketWatcherService ticketWatcherService;
  @Mock
  private AttachmentService attachmentService;
  @Mock
  private CommentService messageService;
  @Mock
  private UserService userService;
  @Mock
  private CustomFieldService fieldService;
  @InjectMocks
  private TicketService ticketService;

  private List<FieldDataEntry> fieldDataEntryList;
  private List<Long> watcherIdList;
  private List<UserEntry> watchersEntryList;
  private List<Ticket> ticketList;
  private List<TicketEntry> ticketEntryList;
  private List<FieldDataEntry> customFieldDataEntryList;

  @Before
  public void initMocks() {
    MockitoAnnotations.initMocks(this);
    List<FieldData> fieldDataList = new ArrayList<>();
    fieldDataList.add(PojoGenerator.getFieldData(SUB_CATEGORY_ID, CATEGORY_ID, FIELD_DATA_ID,
        TICKET_ID, FIELD_ID, ROLE_ID, ENTITY_ID));

    List<FieldDataEntry> fieldDataEntryList = new ArrayList<>();
    fieldDataEntryList.add(PojoGenerator.getCustomFieldDataEntry(FIELD_DATA_ID, TICKET_ID));
    this.fieldDataEntryList = fieldDataEntryList;

    List<Ticket> ticketList = new ArrayList<>();
    ticketList
        .add(PojoGenerator.getTicket(SUB_CATEGORY_ID, CATEGORY_ID, TICKET_ID, ROLE_ID, ENTITY_ID));
    this.ticketList = ticketList;

    List<TicketEntry> ticketEntryList = new ArrayList<>();
    ticketEntryList.add(PojoGenerator.getTicketEntry(SUB_CATEGORY_ID, CATEGORY_ID, TICKET_ID,
        FIELD_ID, FIELD_DATA_ID, ROLE_ID));
    this.ticketEntryList = ticketEntryList;

    List<UserEntry> watchersEntryList = new ArrayList<>();
    watchersEntryList.add(PojoGenerator.getWatcherEntry(WATCHER_ID));

    List<Long> id = new ArrayList<>();
    id.add(1l);
    this.watcherIdList = id;

    List<UserEntry> watchersList = new ArrayList<>();
    watchersList.add(PojoGenerator.getWatcher(WATCHER_ID));

    List<FieldDataEntry> customFieldDataEntryList = new ArrayList<>();
    customFieldDataEntryList.add(PojoGenerator.getCustomFieldDataEntry(4l, TICKET_ID));
    this.customFieldDataEntryList = customFieldDataEntryList;
  }


  @Test
  public void convertToEntityTest() {
    TicketEntry ticketEntry = PojoGenerator.getTicketEntry(SUB_CATEGORY_ID, CATEGORY_ID, TICKET_ID,
        FIELD_ID, FIELD_DATA_ID, ROLE_ID);
    ticketEntry.setPriority(null);
    try {
      Ticket entity = ticketService.convertToEntity(ticketEntry);
      assertTrue(PojoGenerator
          .getTicket(SUB_CATEGORY_ID, CATEGORY_ID, TICKET_ID, ROLE_ID, ENTITY_ID).equals(entity));
    } catch (Exception e) {
      e.printStackTrace();
    }

  }

  @Test
  public void findByIdTest() throws ResourceNotFoundException, BaseException {
    when(ticketDao.findByIdAndActiveTrue(TICKET_ID)).thenReturn(java.util.Optional.ofNullable(
        PojoGenerator.getTicket(SUB_CATEGORY_ID, CATEGORY_ID, TICKET_ID, ROLE_ID, ENTITY_ID)));
    Ticket ticket = ticketService.fetchEntityById(TICKET_ID);
    assertTrue(PojoGenerator.getTicket(SUB_CATEGORY_ID, CATEGORY_ID, TICKET_ID, ROLE_ID, ENTITY_ID)
        .equals(ticket));
  }

  @Test
  public void findByIdTest1() throws ResourceNotFoundException, BaseException {
    when(ticketDao.findByIdAndActiveTrue(TICKET_ID)).thenReturn(Optional.empty());
    Ticket ticket = ticketService.fetchEntityById(TICKET_ID);
    assertTrue(ticket == null);
  }


  @Test
  public void patchUpdateTest() throws ResourceNotFoundException, BaseException {
    TicketEntry entry = PojoGenerator.getTicketEntry(SUB_CATEGORY_ID, CATEGORY_ID, TICKET_ID,
        FIELD_ID, FIELD_DATA_ID, ROLE_ID);
    Ticket ticket =
        PojoGenerator.getTicket(SUB_CATEGORY_ID, CATEGORY_ID, TICKET_ID, ROLE_ID, ENTITY_ID);
    ticket.setAttachments(PojoGenerator.getAttachments(TICKET_ID, MESSAGE_ID, ATTACHMENT_ID));
    List<AttachmentEntry> attachments =
        PojoGenerator.getAttachmentEntries(TICKET_ID, MESSAGE_ID, ATTACHMENT_ID);
    entry.setAttachments(attachments);
    when(ticketDao.findByIdAndActiveTrue(TICKET_ID)).thenReturn(java.util.Optional.ofNullable(
        PojoGenerator.getTicket(SUB_CATEGORY_ID, CATEGORY_ID, TICKET_ID, ROLE_ID, ENTITY_ID)));
    when(ticketCategoryService.fetchEntityById(Mockito.any()))
        .thenReturn(PojoGenerator.getTicketCategory(CATEGORY_ID));
    when(ticketDao.save(Mockito.any())).thenReturn(ticket);

    TicketEntry ticketEntry = ticketService.patchUpdate(TICKET_ID, entry);
    entry.setAssignedUser(null);
    ticketEntry.setAssignedQueueId(ROLE_ID);
    entry.setFields(null);
    assertTrue(entry.equals(ticketEntry));
  }

  @Test(expected = ResourceNotFoundException.class)
  public void patchUpdateTest1() throws ResourceNotFoundException, BaseException {
    TicketEntry entry = PojoGenerator.getTicketEntry(SUB_CATEGORY_ID, CATEGORY_ID, TICKET_ID,
        FIELD_ID, FIELD_DATA_ID, ROLE_ID);
    when(ticketDao.findByIdAndActiveTrue(TICKET_ID)).thenReturn(Optional.empty());
    TicketEntry ticketEntry = ticketService.patchUpdate(TICKET_ID, entry);
    assertTrue(ticketEntry == null);
  }
}
